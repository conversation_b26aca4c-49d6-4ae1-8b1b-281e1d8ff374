# 知识图谱项目 - 节点拖拽参数调节指南

## 📋 概述

本文档记录了节点拖拽功能的参数配置和调优方法，帮助开发者根据需求调整拖拽行为的响应性、速度和幅度。

## 🎯 拖拽行为设计理念

### 设计目标
- 节点在拖拽时仍受物理引擎影响（引力、斥力、碰撞等）
- 鼠标作为"引导力"，吸引节点向鼠标方向移动
- 保持自然的物理交互效果和连带移动
- 提供精确的用户控制体验

### 实现原理
通过向被拖拽节点施加朝向鼠标位置的**引导力**，而不是直接固定节点位置，让节点在物理引擎和用户操作之间找到平衡。

## ⚙️ 核心参数配置

### 1. 引导力计算参数

**位置**：`GraphCanvas.vue` - `handlePointerMove` 函数

```javascript
// 基础引导力系数 - 控制节点对鼠标移动的敏感度
const forceStrength = Math.min(distance * 0.3, 150)

// 远距离增强 - 当鼠标距离节点较远时的额外推动
if (distance > 100) {
  forceStrength *= 1.5 // 远距离时增加50%的力度
}
```

| 参数 | 当前值 | 作用 | 调整建议 |
|------|--------|------|----------|
| `distance * 0.3` | 0.3 | 基础敏感度系数 | 增大=更敏感，减小=更迟钝 |
| `150` | 150 | 最大引导力限制 | 增大=允许更强推动力 |
| `100` | 100 | 远距离阈值 | 调整触发额外推动的距离 |
| `1.5` | 1.5 | 远距离力度倍数 | 增大=远距离推动更强 |

### 2. 力的应用参数

```javascript
// 引导力应用到节点速度的强度
node.vx = (node.vx || 0) + unitX * forceStrength * 0.4
node.vy = (node.vy || 0) + unitY * forceStrength * 0.4
```

| 参数 | 当前值 | 作用 | 调整建议 |
|------|--------|------|----------|
| `0.4` | 0.4 | 力的应用强度 | 增大=更快响应，减小=更平滑 |

### 3. 速度限制参数

```javascript
// 最大移动速度限制
const maxVelocity = 30
```

| 参数 | 当前值 | 作用 | 调整建议 |
|------|--------|------|----------|
| `30` | 30 | 最大移动速度 | 增大=允许更快移动 |

### 4. 拖拽结束参数

```javascript
// 拖拽结束时的速度衰减
if (node.vx) node.vx *= 0.8
if (node.vy) node.vy *= 0.8
```

| 参数 | 当前值 | 作用 | 调整建议 |
|------|--------|------|----------|
| `0.8` | 0.8 | 释放时速度保持率 | 增大=保持更多动量，减小=快速停止 |

## 🔧 常见调优场景

### 场景1：节点响应太慢
**症状**：鼠标移动时节点跟随缓慢
**解决方案**：
- 增加基础敏感度系数：`0.3` → `0.5`
- 增加力的应用强度：`0.4` → `0.6`

### 场景2：节点移动过于激烈
**症状**：轻微拖拽导致节点剧烈移动
**解决方案**：
- 减少基础敏感度系数：`0.3` → `0.2`
- 减少最大引导力：`150` → `100`

### 场景3：大幅拖拽效果不明显
**症状**：鼠标大幅移动时节点移动幅度不够
**解决方案**：
- 增加最大引导力：`150` → `200`
- 增加最大速度：`30` → `40`
- 增加远距离力度倍数：`1.5` → `2.0`

### 场景4：拖拽结束后移动距离不够
**症状**：松开鼠标后节点很快停止，没有到达期望位置
**解决方案**：
- 增加速度保持率：`0.8` → `0.9`
- 增加最大速度：`30` → `35`

### 场景5：小幅拖拽时节点抖动
**症状**：细微鼠标移动导致节点不稳定
**解决方案**：
- 减少基础敏感度：`0.3` → `0.25`
- 增加最小距离阈值（新增参数）

## 📊 参数组合推荐

### 保守型配置（稳定优先）
```javascript
const forceStrength = Math.min(distance * 0.2, 100)
const applyStrength = 0.3
const maxVelocity = 20
const velocityKeep = 0.7
```

### 平衡型配置（当前默认）
```javascript
const forceStrength = Math.min(distance * 0.3, 150)
const applyStrength = 0.4
const maxVelocity = 30
const velocityKeep = 0.8
```

### 激进型配置（响应优先）
```javascript
const forceStrength = Math.min(distance * 0.5, 200)
const applyStrength = 0.6
const maxVelocity = 40
const velocityKeep = 0.9
```

## 🧪 测试方法

### 测试页面
使用 `drag-test.html` 进行参数测试：
```
http://localhost:3003/drag-test.html
```

### 测试场景
1. **微调测试**：小幅度移动鼠标，观察节点响应
2. **大幅测试**：快速大幅移动鼠标，观察移动幅度
3. **缩放测试**：在不同缩放级别下测试拖拽精度
4. **连续测试**：连续拖拽多个节点，观察整体效果

### 调试信息
在浏览器控制台中可以查看：
- 节点当前速度：`node.vx`, `node.vy`
- 引导力大小：`forceStrength`
- 鼠标到节点距离：`distance`

## 📝 修改记录

### v1.0 (2024-07-31)
- 初始实现基于引导力的拖拽系统
- 基础参数：敏感度0.3，最大力150，应用强度0.4

### v1.1 (2024-07-31)
- 增加远距离增强机制
- 提高最大速度到30
- 优化释放时速度保持率到0.8

## 🔮 未来优化方向

1. **自适应参数**：根据节点大小和连接数动态调整参数
2. **用户偏好**：允许用户在UI中调整拖拽敏感度
3. **性能优化**：在大量节点时自动降低计算精度
4. **手势识别**：识别不同的拖拽手势（快拖、慢拖、抖动等）

---

**注意**：修改参数后建议在多种场景下测试，确保不会影响其他交互功能。
